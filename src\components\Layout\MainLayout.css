/* Main Layout - GitHub Style with Neomorphic Design */
.app-layout {
  min-height: 100vh;
  background: var(--background);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Desktop Layout */
.app-layout.desktop {
  display: flex;
  flex-direction: column;
}

.layout-container {
  margin-left: var(--sidebar-width);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background);
  transition: margin-left var(--transition-normal);
}

.layout-container.sidebar-collapsed {
  margin-left: var(--sidebar-collapsed-width);
}

/* GitHub-style Top Header */
.layout-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--secondary-bg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: calc(var(--spacing-md) + 8px) var(--spacing-lg) var(--spacing-md);
  display: grid;
  grid-template-columns: minmax(200px, 1fr) minmax(320px, 2fr) minmax(200px, 1fr);
  align-items: center;
  gap: var(--spacing-lg);
  min-height: 64px;
}

/* Enhanced Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 16px;
  font-weight: 600;
  justify-self: start;
  transition: all 0.3s ease;
  padding: 4px 0;
}

.breadcrumb-item {
  color: var(--text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb-item.current {
  color: var(--accent-color);
  text-shadow: 0 0 8px rgba(51, 189, 148, 0.4);
}

.breadcrumb-separator {
  color: var(--text-secondary);
  margin: 0 var(--spacing-xs);
}

/* Header Actions & Stats */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  justify-self: end;
}

/* Grid-Aligned Header Search Container */
.header-search {
  position: relative;
  width: 100%;
  justify-self: center;
  z-index: 10;
}

/* Search Bar Animation Keyframes */
@keyframes searchPulse {
  0%, 100% {
    box-shadow:
      inset 3px 3px 8px rgba(0, 0, 0, 0.25),
      inset -2px -2px 6px rgba(255, 255, 255, 0.03),
      0 1px 3px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow:
      inset 3px 3px 8px rgba(0, 0, 0, 0.25),
      inset -2px -2px 6px rgba(255, 255, 255, 0.03),
      0 1px 3px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(51, 189, 148, 0.1);
  }
}

@keyframes searchGlow {
  0%, 100% {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(51, 189, 148, 0.2));
  }
}

/* Fixed Neumorphic Search Shell - NO CLIPPING */
.header-search .search-shell.neu {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 18px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow:
    inset 2px 2px 6px rgba(0, 0, 0, 0.2),
    inset -1px -1px 3px rgba(255, 255, 255, 0.02),
    0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.header-search .search-shell.neu::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.08),
    transparent);
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.header-search .search-shell.neu:focus-within {
  border-color: rgba(51, 189, 148, 0.3);
  background: var(--bg-secondary);
  box-shadow:
    inset 3px 3px 8px rgba(0, 0, 0, 0.25),
    inset -2px -2px 6px rgba(255, 255, 255, 0.03),
    0 0 0 3px rgba(51, 189, 148, 0.12),
    0 2px 6px rgba(0, 0, 0, 0.12);
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
  transform: translateY(-1px);
}

.header-search .search-shell.neu:focus-within::before {
  opacity: 0.6;
  background: linear-gradient(90deg,
    transparent,
    rgba(51, 189, 148, 0.2),
    transparent);
}

.header-search .search-shell.neu:hover:not(:focus-within) {
  border-color: rgba(255, 255, 255, 0.12);
  background: var(--bg-secondary);
  box-shadow:
    inset 3px 3px 8px rgba(0, 0, 0, 0.25),
    inset -2px -2px 6px rgba(255, 255, 255, 0.03),
    0 1px 3px rgba(0, 0, 0, 0.12);
  transform: translateY(-0.5px);
}

/* Fixed Search Icon Container - NO CLIPPING */
.header-search .search-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin-right: 12px;
}

.header-search .search-icon {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  transition: all 0.3s ease;
}

.header-search .search-shell.neu:focus-within .search-icon {
  color: var(--accent-color);
}

.header-search .search-shell.neu:hover:not(:focus-within) .search-icon {
  color: rgba(255, 255, 255, 0.7);
}

/* Fixed Search Input - NO OVERLAP */
.header-search .search-input {
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 400;
  flex: 1;
  padding: 0;
  margin: 0;
  line-height: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
  min-width: 0;
  width: 100%;
}

.header-search .search-input::placeholder {
  color: rgba(255, 255, 255, 0.65);
  font-weight: 400;
  font-style: normal;
  transition: all 0.3s ease;
}

.header-search .search-shell.neu:focus-within .search-input {
  color: rgba(255, 255, 255, 0.95);
}

.header-search .search-shell.neu:focus-within .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.header-search .search-shell.neu:hover:not(:focus-within) .search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Fixed Clear Search Button - NO OVERLAP */
.header-search .clear-search {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  min-width: 24px;
  flex-shrink: 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  margin-left: 8px;
  box-shadow:
    inset 1px 1px 2px rgba(0, 0, 0, 0.2),
    inset -1px -1px 2px rgba(255, 255, 255, 0.02);
}

.header-search .clear-search:hover {
  background: rgba(255, 255, 255, 0.06);
  color: var(--text-primary);
  border-color: rgba(255, 255, 255, 0.12);
  box-shadow:
    inset 2px 2px 4px rgba(0, 0, 0, 0.25),
    inset -1px -1px 3px rgba(255, 255, 255, 0.03);
  transform: scale(1.05);
}

.header-search .clear-search:active {
  transform: scale(0.95);
  box-shadow:
    inset 3px 3px 6px rgba(0, 0, 0, 0.3),
    inset -1px -1px 2px rgba(255, 255, 255, 0.01);
}

/* Enhanced Responsive Design for Search Bar */

/* Large Desktop (1440px+) - Optimal spacing and sizing */
@media (min-width: 1440px) {
  .header-search .search-shell.neu {
    padding: 14px 20px;
  }

  .header-search .search-icon-wrapper {
    width: 26px;
    height: 26px;
    margin-right: 14px;
  }

  .header-search .search-input {
    font-size: 15px;
    line-height: 22px;
  }

  .header-search .search-icon {
    font-size: 17px;
  }

  .header-search .clear-search {
    width: 26px;
    height: 26px;
    min-width: 26px;
    font-size: 16px;
    margin-left: 10px;
  }
}

/* Standard Desktop (1024px-1439px) */
@media (max-width: 1439px) and (min-width: 1024px) {
  .header-content {
    grid-template-columns: minmax(180px, 1fr) minmax(300px, 2fr) minmax(180px, 1fr);
    gap: var(--spacing-md);
  }

  .header-search .search-shell.neu {
    padding: 12px 18px;
  }

  .header-search .search-icon-wrapper {
    width: 24px;
    height: 24px;
    margin-right: 12px;
  }

  .header-search .search-input {
    font-size: 14px;
    line-height: 20px;
  }

  .header-search .search-icon {
    font-size: 16px;
  }
}

/* Tablet (768px-1023px) - Balanced layout */
@media (max-width: 1023px) and (min-width: 768px) {
  .header-content {
    grid-template-columns: minmax(140px, 1fr) minmax(260px, 2fr) minmax(140px, 1fr);
    gap: var(--spacing-md);
    padding: calc(var(--spacing-md) + 8px) var(--spacing-lg) var(--spacing-md);
  }

  .breadcrumb {
    font-size: 15px;
  }

  .header-search .search-shell.neu {
    padding: 10px 14px;
  }

  .header-search .search-icon-wrapper {
    width: 22px;
    height: 22px;
    margin-right: 10px;
  }

  .header-search .search-input {
    font-size: 13px;
    line-height: 18px;
  }

  .header-search .search-icon {
    font-size: 15px;
  }

  .header-search .clear-search {
    width: 20px;
    height: 20px;
    min-width: 20px;
    font-size: 14px;
    padding: 3px;
    margin-left: 6px;
  }
}

/* Mobile (max-width: 767px) - Compact design */
@media (max-width: 767px) {
  .header-content {
    grid-template-columns: minmax(100px, 1fr) minmax(200px, 2fr) minmax(100px, 1fr);
    gap: var(--spacing-sm);
    padding: calc(var(--spacing-md) + 8px) var(--spacing-md) var(--spacing-md);
  }

  .breadcrumb {
    font-size: 13px;
    gap: var(--spacing-xs);
  }

  .header-search .search-shell.neu {
    padding: 8px 12px;
    border-radius: 10px;
  }

  .header-search .search-icon-wrapper {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }

  .header-search .search-input {
    font-size: 12px;
    line-height: 16px;
  }

  .header-search .search-input::placeholder {
    font-size: 12px;
  }

  .header-search .search-icon {
    font-size: 14px;
  }

  .header-search .clear-search {
    width: 18px;
    height: 18px;
    min-width: 18px;
    font-size: 14px;
    padding: 2px;
    border-radius: 4px;
    margin-left: 4px;
  }
}

/* Small Mobile (max-width: 480px) - Ultra compact */
@media (max-width: 480px) {
  .header-content {
    grid-template-columns: minmax(80px, 1fr) minmax(180px, 2fr) minmax(80px, 1fr);
    gap: var(--spacing-xs);
    padding: calc(var(--spacing-md) + 8px) var(--spacing-sm) var(--spacing-md);
  }

  .breadcrumb {
    font-size: 12px;
  }

  .breadcrumb-separator {
    margin: 0 2px;
  }

  .header-search .search-shell.neu {
    padding: 6px 10px;
    border-radius: 8px;
  }

  .header-search .search-icon-wrapper {
    width: 18px;
    height: 18px;
    margin-right: 6px;
  }

  .header-search .search-input {
    font-size: 11px;
    line-height: 14px;
  }

  .header-search .search-input::placeholder {
    font-size: 11px;
  }

  .header-search .search-icon {
    font-size: 12px;
  }

  .header-search .clear-search {
    width: 16px;
    height: 16px;
    min-width: 16px;
    font-size: 12px;
    padding: 2px;
    border-radius: 3px;
    margin-left: 4px;
  }
}

/* Search Drawer - Small Pop-out Panel */
.search-drawer {
  position: fixed;
  right: 24px;
  top: 72px;
  width: min(420px, 90vw);
  max-height: 80vh;
  background: var(--secondary-bg);
  border-radius: 18px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.08);
  z-index: 50;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.search-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.search-drawer-header h4 {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.close-search {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all var(--transition-fast);
}

.close-search:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.search-drawer-content {
  padding: 16px 20px;
  overflow-y: auto;
  max-height: calc(80vh - 70px);
}

.search-suggestion {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--background);
  border-radius: 12px;
  color: var(--text-secondary);
  font-size: 14px;
  box-shadow: 
    inset 1px 1px 2px rgba(0, 0, 0, 0.2),
    inset -1px -1px 2px rgba(255, 255, 255, 0.03);
}

.header-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--background);
  border-radius: var(--radius-md);
  box-shadow:
    var(--shadow-neumorphic),
    0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all var(--transition-normal);
  min-width: 60px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-hover);
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 2px;
  font-weight: 500;
}

/* Main Content Area - CSS Grid Layout */
.layout-main {
  display: grid;
  grid-template-columns: minmax(0, 1fr);
  gap: var(--spacing-xl);
  max-width: 1440px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-xl) 0;
  align-items: start;
  flex: 1;
  min-height: 0;
  height: auto;
}

/* Responsive Grid Columns - Updated */
@media (min-width: 1440px) {
  .layout-main {
    grid-template-columns: minmax(0, 1fr) 320px;
    gap: 2rem;
    max-width: 1440px;
  }
}

@media (min-width: 1280px) and (max-width: 1439px) {
  .layout-main {
    grid-template-columns: minmax(0, 1fr) 320px;
    gap: 2rem;
    max-width: 1280px;
  }
}

@media (max-width: 1279px) and (min-width: 1024px) {
  .layout-main {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .sidebar-column {
    display: none;
  }
}

@media (max-width: 1023px) {
  .layout-main {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .sidebar-column {
    display: none;
  }
}

/* Content Column */
.content-column {
  min-height: 600px;
  width: 100%;
  min-width: 0;
  height: auto;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  width: 100%;
  max-width: 860px;
  margin: 0 auto;
  background: var(--secondary-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.05);
  min-height: 500px;
  position: relative;
  padding-right: 8px;
  height: auto;
  overflow: visible;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  opacity: 0.6;
}

/* Subtle Dark Theme Scrollbars */
.content-wrapper::-webkit-scrollbar,
.search-drawer-content::-webkit-scrollbar {
  width: 6px;
}

.content-wrapper::-webkit-scrollbar-track,
.search-drawer-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb,
.search-drawer-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.content-wrapper::-webkit-scrollbar-thumb:hover,
.search-drawer-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Sidebar Column */
.sidebar-column {
  position: sticky;
  top: calc(64px + var(--spacing-xl));
  max-height: calc(100vh - 64px - var(--spacing-xl) * 2);
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.sidebar-column::-webkit-scrollbar {
  display: none;
}

.sidebar-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* Sidebar Cards - Smoother Neomorphism */
.sidebar-card {
  background: var(--secondary-bg);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.06);
  box-shadow: var(--shadow-neumorphic-soft);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(8px);
}

.sidebar-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(51, 189, 148, 0.3), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.sidebar-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-neumorphic-elevated);
  border-color: rgba(255, 255, 255, 0.08);
}

.sidebar-card:hover::before {
  opacity: 1;
}

.card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.02) 0%, 
    rgba(255, 255, 255, 0.005) 100%);
}

.card-header h3 {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.01em;
}

.card-content {
  padding: 24px;
}

.profile-summary {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

/* Smoother Profile Elements */
.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--accent-color);
  box-shadow: 
    0 0 15px rgba(51, 189, 148, 0.25),
    0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
  position: relative;
}

.profile-avatar::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-secondary));
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.profile-avatar:hover {
  transform: scale(1.05);
  box-shadow: 
    0 0 20px rgba(51, 189, 148, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-avatar:hover::before {
  opacity: 1;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info h4 {
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.profile-info p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0 0 var(--spacing-sm) 0;
}

.profile-stats {
  display: flex;
  gap: var(--spacing-md);
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.stat-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 2px;
}

.bio {
  margin-bottom: var(--spacing-md);
}

.bio p {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

/* Smoother Interactive Elements */
.skill-tag {
  background: rgba(255, 255, 255, 0.04);
  color: var(--accent-color);
  padding: 6px 14px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: var(--shadow-neumorphic-inset);
  transition: all var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.06);
}

.skill-tag:hover {
  background: var(--accent-color);
  color: var(--background);
  box-shadow: 
    0 3px 8px rgba(51, 189, 148, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  border-color: var(--accent-color);
}

.trending-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.trending-item, .suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
  box-shadow: var(--shadow-neumorphic-inset);
  transition: all var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.04);
}

.trending-item:hover, .suggestion-item:hover {
  background: rgba(255, 255, 255, 0.04);
  box-shadow: var(--shadow-neumorphic-soft);
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.08);
}

.trending-tag {
  color: var(--accent-color);
  font-weight: 600;
  font-size: 14px;
}

.trending-count {
  color: var(--text-secondary);
  font-size: 12px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.suggestion-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--accent-color);
}

.suggestion-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.suggestion-info {
  flex: 1;
}

.suggestion-info h5 {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 2px 0;
}

.suggestion-info p {
  color: var(--text-secondary);
  font-size: 12px;
  margin: 0;
}

.follow-btn {
  background: var(--accent-color);
  color: var(--background);
  border: none;
  padding: 8px 18px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: 
    0 2px 6px rgba(51, 189, 148, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.follow-btn:hover {
  background: var(--accent-secondary);
  box-shadow: 
    0 4px 10px rgba(51, 189, 148, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.follow-btn:active {
  transform: translateY(0);
  box-shadow: 
    0 1px 3px rgba(51, 189, 148, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Mobile Styles */
.app-layout.mobile .mobile-main {
  padding-top: 50px;
  padding-bottom: 60px;
  min-height: 100vh;
  background: var(--background);
}

/* Responsive Breakpoints */
@media (max-width: 1200px) {
  .layout-main {
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-lg) 0;
  }

  .header-content {
    padding: var(--spacing-md) var(--spacing-md);
    grid-template-columns: minmax(150px, 1fr) minmax(250px, 2fr) minmax(150px, 1fr);
    gap: var(--spacing-md);
  }

  .header-search {
    max-width: 400px;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .layout-container {
    margin-left: 72px;
  }

  .layout-main {
    grid-template-columns: 1fr;
    padding-left: var(--spacing-md);
  }

  .sidebar-column {
    order: 2;
    display: block;
    margin-top: var(--spacing-xl);
  }

  .header-stats {
    display: none;
  }

  .stat-item {
    min-width: 50px;
  }
}

@media (max-width: 768px) {
  .layout-main {
    grid-template-columns: 1fr;
  }

  .sidebar-column {
    display: none;
  }

  .header-stats {
    display: none;
  }
}

@media (max-width: 768px) {
  .app-layout.desktop {
    display: none;
  }

  .app-layout.mobile {
    display: block;
  }

  .header-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: var(--spacing-sm);
    text-align: center;
    padding: var(--spacing-sm);
  }

  .breadcrumb {
    justify-self: center;
    order: 1;
  }

  .header-search {
    justify-self: stretch;
    max-width: none;
    order: 2;
  }

  .header-actions {
    justify-self: center;
    order: 3;
  }

  .layout-main {
    padding: var(--spacing-md) var(--spacing-sm);
    gap: var(--spacing-md);
  }

  .content-wrapper {
    border-radius: var(--radius-md);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .sidebar-card {
    border: 2px solid var(--accent-color);
  }
  
  .trending-item, .suggestion-item {
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* Animation Delays for Staggered Loading */
.sidebar-card {
  animation: slideInRight 0.6s ease-out;
}

.sidebar-card:nth-child(2) {
  animation-delay: 0.1s;
}

.sidebar-card:nth-child(3) {
  animation-delay: 0.2s;
} 