.search-drawer-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
}

.search-drawer-backdrop {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  animation: fadeIn 0.2s ease-out;
}

.search-drawer {
  position: relative;
  height: 100%;
  width: 90vw;
  max-width: 420px;
  background: var(--background);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.45);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

.search-drawer-close {
  position: absolute;
  right: var(--spacing-md);
  top: var(--spacing-md);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  padding: var(--spacing-xs);
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 2;
}

.search-drawer-close:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
}

.search-drawer-content {
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  height: 100%;
  overflow-y: auto;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
} 