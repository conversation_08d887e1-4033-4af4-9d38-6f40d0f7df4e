.message-board {
  display: flex;
  height: calc(100vh - 120px);
  gap: 20px;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.chat-sidebar {
  flex: 0 0 350px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.action-btn:hover {
  color: var(--accent-color);
  background: var(--secondary-bg);
}

.search-bar {
  margin: 15px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-icon {
  color: var(--text-secondary);
}

.search-bar input {
  background: none;
  border: none;
  color: var(--text-primary);
  width: 100%;
}

.search-bar input::placeholder {
  color: var(--text-secondary);
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.chat-item {
  display: flex;
  padding: 15px;
  gap: 15px;
  cursor: pointer;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.chat-item:hover {
  transform: translateY(-2px);
}

.chat-item.active {
  background: var(--secondary-bg);
  border: 1px solid var(--accent-color);
}

.chat-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.chat-time {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.chat-preview {
  color: var(--text-secondary);
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
  font-size: 0.8rem;
}

.member-count {
  color: var(--text-secondary);
}

.unread-badge {
  background: var(--accent-color);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
}

.chat-main-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.message-input-container {
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 15px;
}

.input-action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.input-action-btn:hover {
  color: var(--accent-color);
  background: var(--secondary-bg);
}

.message-input {
  flex: 1;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1rem;
}

.message-input::placeholder {
  color: var(--text-secondary);
}

.send-btn {
  background: var(--accent-color);
  border: none;
  color: white;
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.send-btn:hover {
  transform: scale(1.1);
}

.no-chat-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .message-board {
    flex-direction: column;
    height: calc(100vh - 140px);
  }

  .chat-sidebar {
    flex: 0 0 auto;
    max-height: 300px;
  }
} 