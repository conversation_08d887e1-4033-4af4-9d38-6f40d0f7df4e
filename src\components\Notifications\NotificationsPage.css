.notifications-container {
  max-width: 580px;
  margin: 0 auto;
  padding: 1.5rem;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.notifications-header h1 {
  font-size: 1.5rem;
  margin: 0;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 15px;
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-button:hover {
  background: var(--input-bg);
}

.notifications-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.notifications-filters::-webkit-scrollbar {
  display: none;
}

.filter-option {
  padding: 0.6rem 1.2rem;
  background: var(--secondary-bg);
  border: none;
  border-radius: 15px;
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-option.active {
  background: var(--accent-color);
  color: var(--text-primary);
}

.filter-option:not(.active):hover {
  background: var(--input-bg);
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-card {
  display: grid;
  grid-template-columns: auto auto 1fr auto;
  gap: 1rem;
  padding: 1.2rem;
  border-radius: 8px;
  position: relative;
  align-items: flex-start;
}

.notification-card.unread {
  background: rgba(51, 189, 148, 0.05);
}

.notification-card.unread:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 4px;
  height: 30%;
  background: var(--accent-color);
  border-radius: 0 4px 4px 0;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--input-bg);
}

.notification-type-icon {
  font-size: 1rem;
  color: var(--text-secondary);
}

.notification-type-icon.connection {
  color: #0a66c2;
}

.notification-type-icon.like {
  color: #f5222d;
}

.notification-type-icon.comment {
  color: #52c41a;
}

.notification-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
}

.notification-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification-text {
  margin: 0;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.notification-comment {
  background: var(--input-bg);
  padding: 0.8rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.notification-image {
  width: 100%;
  max-width: 200px;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.notification-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.notification-more {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.notification-more:hover {
  color: var(--text-primary);
}

.no-notifications {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

@media (max-width: 576px) {
  .notifications-container {
    padding: 1rem;
  }

  .notification-card {
    grid-template-columns: auto 1fr auto;
  }

  .notification-icon {
    display: none;
  }
} 