.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  color: var(--text-primary);
  background: var(--background);
  min-height: 100vh;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
}

.back-btn {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
}

.profile-header h1 {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 500;
}

.profile-search {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.8rem 1rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1rem;
}

.profile-main {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-details h2 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
}

.profile-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.edit-btn {
  background: transparent;
  border: 1px solid var(--accent-color);
  color: var(--accent-color);
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.profile-location {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-icon {
  color: var(--accent-color);
  font-size: 1.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.profile-skills {
  margin-bottom: 2rem;
}

.profile-skills h3 {
  font-size: 1.2rem;
  margin: 0 0 1rem;
}

.skills-list {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.skill-tag {
  background: var(--secondary-bg);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.activity-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 2rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.activity-item {
  text-align: center;
}

.activity-value {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
}

.activity-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2px;
}

.post-item {
  aspect-ratio: 1;
  overflow: hidden;
}

.post-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .profile-container {
    padding: 1rem 0.5rem;
  }
  
  .profile-main {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-title {
    justify-content: center;
  }
  
  .posts-grid {
    gap: 1px;
  }
} 