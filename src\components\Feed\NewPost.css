.new-post-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 550px;
  background: var(--primary-bg);
  border-radius: 15px;
  padding: 1.5rem;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.new-post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.new-post-header h2 {
  color: var(--text-primary);
  font-size: 1.3rem;
  margin: 0;
}

.close-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.post-btn {
  background: var(--accent-color);
  color: var(--text-primary);
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.post-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.post-btn:not(:disabled):hover {
  opacity: 0.9;
}

.content-type-selector {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.type-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  background: var(--secondary-bg);
  border: none;
  border-radius: 12px;
  padding: 0.8rem 1rem;
  color: var(--text-secondary);
  font-size: 0.8rem;
  cursor: pointer;
  flex: 1;
  transition: all 0.3s ease;
}

.type-btn svg {
  font-size: 1.2rem;
}

.type-btn.active {
  background: var(--accent-color);
  color: var(--text-primary);
}

.type-btn:hover:not(.active) {
  background: var(--input-bg);
}

.post-content {
  width: 100%;
}

.post-textarea {
  width: 100%;
  min-height: 150px;
  background: var(--input-bg);
  border: none;
  border-radius: 12px;
  padding: 1rem;
  color: var(--text-primary);
  font-size: 1rem;
  resize: none;
}

.post-textarea::placeholder {
  color: var(--text-secondary);
}

.post-textarea:focus {
  outline: none;
}

.add-media-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: var(--input-bg);
  border: none;
  border-radius: 12px;
  padding: 1rem;
  color: var(--accent-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-media-btn:hover {
  background: var(--secondary-bg);
}

.tags-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tags-section h3 {
  color: var(--text-primary);
  font-size: 1.1rem;
  margin: 0;
}

.tag-input-container {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: var(--input-bg);
  border-radius: 12px;
  padding: 0.8rem 1rem;
}

.tag-input-container svg {
  color: var(--text-secondary);
}

.tag-input-container input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.tag-input-container input:focus {
  outline: none;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: var(--secondary-bg);
  color: var(--accent-color);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

@media (max-width: 600px) {
  .new-post-container {
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
  }

  .content-type-selector {
    flex-wrap: wrap;
  }

  .type-btn {
    min-width: 80px;
  }
} 