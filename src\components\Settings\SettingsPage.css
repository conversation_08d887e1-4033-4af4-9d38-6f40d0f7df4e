.settings-page-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 1rem;
  color: var(--text-primary);
}

.settings-page-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.back-btn {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.settings-page-header h1 {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 600;
}

.settings-page-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Account Info Page */
.info-section {
  background: var(--secondary-bg);
  border-radius: 12px;
  overflow: hidden;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 1rem;
  color: var(--accent-color);
  font-size: 1.2rem;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.info-content label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.info-value {
  font-size: 1rem;
  color: var(--text-primary);
}

.edit-btn {
  background: transparent;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0.5rem;
}

/* Notifications Page */
.notification-channels, .notification-settings {
  background: var(--secondary-bg);
  border-radius: 12px;
  padding: 1.5rem;
}

.notification-channels h2, .notification-settings h2 {
  font-size: 1.2rem;
  margin: 0 0 1rem;
  color: var(--text-primary);
}

.channel-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.channel-item {
  display: flex;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid var(--border-color);
}

.channel-item:last-child {
  border-bottom: none;
}

.channel-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 1rem;
  color: var(--accent-color);
  font-size: 1.2rem;
}

.channel-content {
  flex: 1;
}

.channel-title {
  font-size: 1rem;
  color: var(--text-primary);
  margin-bottom: 0.2rem;
}

.channel-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--input-bg);
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: var(--text-secondary);
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--accent-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
  background-color: var(--text-primary);
}

.setting-category {
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.setting-category:last-child {
  border-bottom: none;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.category-header h3 {
  font-size: 1.1rem;
  margin: 0;
  color: var(--text-primary);
}

.category-toggles {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.toggle-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Security Page */
.security-section {
  background: var(--secondary-bg);
  border-radius: 12px;
  padding: 1.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.section-header h2 {
  font-size: 1.2rem;
  margin: 0;
  color: var(--text-primary);
}

.section-description {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0 0 1.5rem;
  line-height: 1.5;
}

.security-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.device-item:last-child {
  border-bottom: none;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.device-name {
  font-size: 1rem;
  color: var(--text-primary);
}

.device-location, .device-activity {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.current-badge {
  background: var(--accent-color);
  color: var(--text-primary);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  margin-left: 0.5rem;
}

.danger-section .section-header {
  color: #ff4d4d;
}

/* Buttons */
.primary-btn, .secondary-btn, .danger-btn {
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.primary-btn {
  background: var(--accent-color);
  color: var(--text-primary);
}

.secondary-btn {
  background: var(--input-bg);
  color: var(--text-primary);
}

.danger-btn {
  background: transparent;
  color: #ff4d4d;
  border: 1px solid #ff4d4d;
}

.danger-btn:hover {
  background: #ff4d4d;
  color: var(--text-primary);
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

@media (max-width: 480px) {
  .settings-page-container {
    padding: 0.5rem;
  }
  
  .category-toggles {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .device-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .device-item button {
    align-self: flex-end;
  }
} 