.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  max-width: 800px;
  margin: 0 auto;
  background: var(--background);
  color: var(--text-primary);
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.chat-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.chat-header-info h3 {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.chat-header-info p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.chat-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.chat-action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  font-size: 1.2rem;
}

.chat-action-btn:hover {
  color: var(--accent-color);
  background: rgba(255, 255, 255, 0.1);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  min-height: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-date {
  text-align: center;
  margin: var(--spacing-lg) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.message-item {
  display: flex;
  margin-bottom: var(--spacing-md);
}

.message-item.own {
  justify-content: flex-end;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
}

.message-item.own .message-avatar {
  margin-right: 0;
  margin-left: var(--spacing-sm);
  order: 2;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message-item.own .message-content {
  align-items: flex-end;
}

.message-bubble {
  border-radius: calc(var(--neu-radius) + 6px);
  background: var(--accent-darker);
  box-shadow: none;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: 4px;
  max-width: 100%;
  word-wrap: break-word;
}

.message-item.own .message-bubble {
  background: var(--accent-primary);
  color: var(--bg-primary);
  font-weight: 500;
}

.message-bubble p {
  margin: 0;
  line-height: 1.4;
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-secondary);
  opacity: 0.7;
}

.message-item.own .message-time {
  text-align: right;
}

.chat-input-container {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm);
  min-height: 44px;
}

.chat-input {
  flex: 1;
  background: none;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-size: 1rem;
  resize: none;
  max-height: 120px;
  min-height: 20px;
  padding: var(--spacing-xs) 0;
  line-height: 1.4;
}

.chat-input::placeholder {
  color: var(--text-secondary);
}

.chat-input-actions {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.chat-input-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.chat-input-btn:hover {
  color: var(--accent-primary);
  background: rgba(255, 255, 255, 0.1);
}

.chat-input-btn.send-btn {
  background: var(--accent-primary);
  color: white;
}

.chat-input-btn.send-btn:hover {
  background: var(--accent-secondary);
  color: white;
}

.chat-input-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Message status indicators */
.message-status {
  font-size: 0.75rem;
  color: var(--text-secondary);
  opacity: 0.7;
  margin-top: 2px;
}

.message-status.read {
  color: var(--accent-primary);
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-messages {
    padding: var(--spacing-md);
  }
  
  .chat-input-container {
    padding: var(--spacing-md);
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .chat-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* Animation for new messages */
@keyframes slideInMessage {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-item.new {
  animation: slideInMessage 0.3s ease-out;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  font-style: italic;
  opacity: 0.8;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--text-secondary);
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
} 