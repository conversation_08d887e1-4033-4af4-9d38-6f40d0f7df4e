:root {
  --background: #001a1a;
  --secondary-bg: #002626;
  --accent-color: #33BD94;
  --text-primary: #ffffff;
  --text-secondary: #888888;
  --shadow-dark: rgba(0, 0, 0, 0.3);
  --shadow-light: rgba(255, 255, 255, 0.05);
  --border-color: rgba(255, 255, 255, 0.1);
}

.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  color: var(--text-primary);
  background: var(--background);
  min-height: 100vh;
}

.settings-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
}

.back-btn {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
}

.settings-header h1 {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 500;
}

.settings-search {
  margin-bottom: 1.5rem;
  padding: 0 0.5rem;
}

.search-input {
  width: 100%;
  padding: 0.8rem 1rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1rem;
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.settings-list {
  display: flex;
  flex-direction: column;
  background: var(--secondary-bg);
  border-radius: 12px;
  overflow: hidden;
}

.settings-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  text-decoration: none;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
}

.settings-item:last-child {
  border-bottom: none;
}

.item-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: var(--accent-color);
  font-size: 1.2rem;
}

.item-label {
  flex: 1;
  font-size: 1rem;
}

.item-arrow {
  color: var(--text-secondary);
  font-size: 1.5rem;
  margin-left: 0.5rem;
}

@media (max-width: 768px) {
  .settings-container {
    padding: 1rem 0.5rem;
  }
  
  .settings-header {
    padding: 0.5rem;
  }
  
  .settings-header h1 {
    font-size: 1.3rem;
  }
} 