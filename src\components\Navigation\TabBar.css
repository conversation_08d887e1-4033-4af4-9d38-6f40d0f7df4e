.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--bg-primary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: none;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  padding: 8px 0;
  transition: all 0.3s ease;
  position: relative;
}

.tab-icon {
  font-size: 24px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.tab-label {
  font-size: 11px;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.tab-item:hover {
  color: var(--text-primary);
}

.tab-item:hover .tab-icon {
  transform: translateY(-2px);
}

.tab-item.active {
  color: var(--accent-primary);
}

.tab-item.active .tab-icon {
  transform: scale(1.1);
}

.tab-item.active .tab-label {
  opacity: 1;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  border-radius: 2px;
  background-color: var(--accent-primary);
  box-shadow: 0 0 8px var(--accent-primary);
}

/* Show TabBar on mobile devices */
@media (max-width: 768px) {
  .tab-bar {
    display: flex;
  }

  .tab-icon {
    font-size: 22px;
  }
}

@media (max-width: 480px) {
  .tab-bar {
    height: 50px;
  }

  .tab-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .tab-label {
    font-size: 10px;
  }
} 