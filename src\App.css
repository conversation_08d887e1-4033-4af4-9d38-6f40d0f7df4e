:root {
  --background: #001a1a;
  --secondary-bg: #002626;
  --accent-color: #33BD94;
  --text-primary: #ffffff;
  --text-secondary: #888888;
  --border-color: rgba(255, 255, 255, 0.1);
  
  /* Legacy support - matching theme.css */
  --bg-primary: #002626;
  --bg-secondary: #001a1a;
  --accent-primary: #33BD94;
  --accent-secondary: #2a9d7c;
  
  /* Enhanced Grid System */
  --container-max-width: 1200px;
  --sidebar-width: 245px;
  --sidebar-collapsed-width: 73px;
  --content-max-width: 614px;
  --suggestions-width: 320px;
  
  /* Enhanced Neumorphic Shadows */
  --shadow-neumorphic: 
    8px 8px 16px rgba(0, 0, 0, 0.4),
    -8px -8px 16px rgba(255, 255, 255, 0.05);
  --shadow-neumorphic-inset: 
    inset 8px 8px 16px rgba(0, 0, 0, 0.4),
    inset -8px -8px 16px rgba(255, 255, 255, 0.05);
  --shadow-neumorphic-hover: 
    12px 12px 24px rgba(0, 0, 0, 0.5),
    -12px -12px 24px rgba(255, 255, 255, 0.08);
  --shadow-glow: 0 0 20px rgba(51, 189, 148, 0.3);
  
  /* Legacy shadow support */
  --shadow-outset: -8px -8px 12px rgba(255, 255, 255, 0.05),
                   8px 8px 12px rgba(0, 0, 0, 0.2);
  --shadow-inset: inset -8px -8px 12px rgba(255, 255, 255, 0.05),
                  inset 8px 8px 12px rgba(0, 0, 0, 0.2);
  
  /* Spacing System */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* Border Radius System */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;
  
  /* Typography Hierarchy */
  --font-size-caption: 14px;
  --font-size-body: 16px;
  --font-size-section-title: 20px;

  /* Animation System */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background: var(--background);
  color: var(--text-primary);
  overflow-x: hidden;
  line-height: 1.6;
  font-size: var(--font-size-body);
}

/* Typography Utility Classes */
.text-caption {
  font-size: var(--font-size-caption);
}

.text-body {
  font-size: var(--font-size-body);
}

.text-section-title {
  font-size: var(--font-size-section-title);
  font-weight: 600;
}

.app {
  min-height: 100vh;
  background: var(--background);
}

/* Auth Layout for Login/Signup pages */
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background);
  padding: var(--spacing-lg);
}

/* 12-Column Grid System */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  width: 100%;
}

.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-lg);
  width: 100%;
}

.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-5 { grid-column: span 5; }
.col-6 { grid-column: span 6; }
.col-7 { grid-column: span 7; }
.col-8 { grid-column: span 8; }
.col-9 { grid-column: span 9; }
.col-10 { grid-column: span 10; }
.col-11 { grid-column: span 11; }
.col-12 { grid-column: span 12; }

/* Desktop Layout - Fixed with Grid System */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  padding-top: 80px;
  min-height: 100vh;
  transition: margin-left var(--transition-normal);
  background-color: var(--bg-primary);
  position: relative;
  z-index: 1;
  max-width: calc(100vw - var(--sidebar-width));
}

.main-content.sidebar-collapsed {
  margin-left: var(--sidebar-collapsed-width);
  max-width: calc(100vw - var(--sidebar-collapsed-width));
}

.main-content.auth-layout {
  margin-left: 0;
  padding-top: 0;
  min-height: 100vh;
  max-width: 100vw;
}

/* Content Container with Max Width */
.content-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--spacing-lg);
  position: relative;
  width: 100%;
}

/* Enhanced Homepage Layout */
.homepage-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: var(--spacing-xl);
  max-width: 975px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  align-items: start;
}

/* Scrollable Sections with Better Spacing */
.scrollable-sections {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  padding-right: var(--spacing-sm);
  scroll-behavior: smooth;
}

.scrollable-sections::-webkit-scrollbar {
  width: 6px;
}

.scrollable-sections::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.scrollable-sections::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.scrollable-sections::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Enhanced Section Slides */
.section-slide {
  min-height: 400px;
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-neumorphic);
  transition: all var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.section-slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.section-slide:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-neumorphic-hover);
  border-color: rgba(51, 189, 148, 0.2);
}

.section-slide:hover::before {
  opacity: 1;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-glow);
}

/* Enhanced Code Editor */
.code-editor-section {
  background: #0d1117;
  border: 1px solid #30363d;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-neumorphic-inset);
  transition: all var(--transition-normal);
}

.code-editor-section:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-neumorphic-inset), var(--shadow-glow);
}

.code-editor-header {
  background: linear-gradient(135deg, #161b22, #21262d);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid #30363d;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-editor-title {
  color: #f0f6fc;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.code-editor-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.code-control-btn {
  background: var(--bg-secondary);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 12px;
  padding: 6px 12px;
  box-shadow: var(--shadow-neumorphic);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 4px;
}

.code-control-btn:hover {
  background: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-neumorphic-hover);
}

.code-control-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-neumorphic-inset);
}

.code-editor-content {
  background: #0d1117;
  color: #f0f6fc;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: var(--spacing-lg);
  min-height: 200px;
  overflow-x: auto;
}

.code-line {
  display: flex;
  align-items: center;
  min-height: 20px;
  transition: background var(--transition-fast);
}

.code-line:hover {
  background: rgba(255, 255, 255, 0.02);
}

.line-number {
  color: #6e7681;
  margin-right: var(--spacing-lg);
  min-width: 20px;
  text-align: right;
  user-select: none;
  font-size: 12px;
}

/* Enhanced Syntax Highlighting */
.keyword { color: #ff7b72; font-weight: 600; }
.string { color: #a5d6ff; }
.comment { color: #8b949e; font-style: italic; }
.function { color: #d2a8ff; font-weight: 500; }
.variable { color: #ffa657; }
.number { color: #79c0ff; }
.operator { color: #ff7b72; }

/* Page Layout Utilities */
.page-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: var(--spacing-xl);
  max-width: 975px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-lg);
  align-items: start;
}

.page-main {
  min-width: 0; /* Prevents grid overflow */
}

.page-sidebar {
  width: 320px;
  position: sticky;
  top: 100px;
}

/* Mobile Layout Fixes */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding-top: 50px;
    padding-bottom: 60px;
    max-width: 100vw;
  }
  
  .main-content.auth-layout {
    padding-top: 0;
    padding-bottom: 0;
  }
  
  .homepage-layout,
  .page-layout {
    grid-template-columns: 1fr;
    padding: var(--spacing-md);
    gap: var(--spacing-lg);
  }
  
  .page-sidebar {
    width: 100%;
    position: static;
    order: -1;
  }
  
  .scrollable-sections {
    max-height: none;
    overflow-y: visible;
  }
  
  .content-container {
    padding: var(--spacing-md);
  }
  
  .grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

/* Enhanced Card Components */
.card {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-neumorphic);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-hover);
  border-color: rgba(51, 189, 148, 0.2);
}

.card:hover::before {
  opacity: 1;
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-neumorphic-inset);
}

/* Enhanced Button System */
.btn {
  background: var(--bg-secondary);
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-neumorphic);
  transition: all var(--transition-normal);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transition: all var(--transition-normal);
  transform: translate(-50%, -50%);
}

.btn:hover {
  color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-hover);
}

.btn:hover::before {
  width: 100px;
  height: 100px;
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-neumorphic-inset);
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: var(--text-primary);
  box-shadow: var(--shadow-neumorphic), var(--shadow-glow);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--accent-secondary), var(--accent-primary));
  color: var(--text-primary);
  box-shadow: var(--shadow-neumorphic-hover), var(--shadow-glow);
}

.btn-sm {
  font-size: 0.875rem;
  padding: var(--spacing-sm) var(--spacing-md);
}

.btn-lg {
  font-size: 1.125rem;
  padding: var(--spacing-lg) var(--spacing-xl);
}

/* Enhanced Form Elements */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  box-shadow: var(--shadow-neumorphic-inset);
  transition: all var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.form-input:focus {
  outline: none;
  box-shadow: var(--shadow-neumorphic-inset), 0 0 0 2px var(--accent-primary);
  border-color: var(--accent-primary);
}

.form-input::placeholder {
  color: var(--text-secondary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* Animation Classes */
.fade-in {
  animation: fadeInUp 0.6s var(--transition-normal);
}

.slide-up {
  animation: slideUp 0.4s var(--transition-normal);
}

.bounce-in {
  animation: bounceIn 0.5s var(--transition-normal);
}

.scale-in {
  animation: scaleIn 0.3s var(--transition-normal);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading States */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-spinner::after {
  content: '';
  width: 32px;
  height: 32px;
  border: 4px solid var(--bg-secondary);
  border-top-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Focus and Accessibility */
.btn:focus,
.form-input:focus,
.code-control-btn:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-secondary: #cccccc;
    --border-color: rgba(255, 255, 255, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Subtle Custom Scrollbar for all elements */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: background 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .homepage-layout,
  .page-layout {
    grid-template-columns: 1fr 280px;
    gap: var(--spacing-lg);
  }
  
  .page-sidebar {
    width: 280px;
  }
  
  .content-container {
    padding: var(--spacing-lg) var(--spacing-md);
  }
} 