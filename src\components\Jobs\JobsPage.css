.jobs-container {
  display: flex;
  gap: 1.5rem;
  max-width: 1140px;
  margin: 0 auto;
  padding: 1.5rem;
}

.jobs-sidebar {
  width: 320px;
  border-radius: 8px;
  padding: 1.5rem;
  flex-shrink: 0;
  align-self: flex-start;
  position: sticky;
  top: 80px;
}

.job-search-container {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-bottom: 1.5rem;
}

.search-item {
  display: flex;
  align-items: center;
  background: var(--input-bg);
  border-radius: 4px;
  padding: 0.8rem 1rem;
}

.search-icon {
  margin-right: 0.8rem;
  color: var(--text-secondary);
}

.search-input {
  background: transparent;
  border: none;
  color: var(--text-primary);
  width: 100%;
  font-size: 0.9rem;
}

.search-input:focus {
  outline: none;
}

.search-button {
  background: var(--accent-color);
  color: var(--text-primary);
  border: none;
  border-radius: 4px;
  padding: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.search-button:hover {
  background: var(--accent-color);
  opacity: 0.9;
}

.job-filters {
  margin-bottom: 1.5rem;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.7rem 1rem;
  border: 1px solid var(--border-color);
  background: var(--secondary-bg);
  color: var(--text-primary);
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: var(--input-bg);
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 15px;
  font-size: 0.85rem;
  color: var(--text-primary);
  cursor: pointer;
}

.filter-chip:hover {
  background: var(--input-bg);
}

.jobs-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.jobs-action-link {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  text-decoration: none;
  color: var(--text-primary);
  font-size: 0.9rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.jobs-action-link:hover {
  background: var(--input-bg);
}

.action-icon {
  color: var(--accent-color);
}

.jobs-listings {
  flex: 1;
}

.jobs-section-header {
  margin-bottom: 1.5rem;
}

.jobs-section-header h2 {
  font-size: 1.3rem;
  margin: 0 0 0.5rem;
}

.jobs-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.job-card {
  padding: 1.5rem;
  border-radius: 8px;
}

.job-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.company-logo {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--secondary-bg);
  flex-shrink: 0;
}

.company-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.job-main-info {
  flex: 1;
}

.job-title {
  font-size: 1.1rem;
  margin: 0 0 0.3rem;
  color: var(--accent-color);
}

.company-name {
  font-size: 1rem;
  margin: 0 0 0.3rem;
}

.job-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.location-icon {
  font-size: 0.8rem;
}

.save-job-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.save-job-btn.saved {
  color: var(--accent-color);
}

.job-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.job-actions {
  display: flex;
  gap: 1rem;
}

.apply-btn, .message-btn {
  padding: 0.6rem 1.2rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn {
  background: var(--accent-color);
  color: var(--text-primary);
  border: none;
  flex: 1;
}

.message-btn {
  background: transparent;
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
  flex: 1;
}

.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.filter-modal-content {
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  background: var(--secondary-bg);
  border-radius: 8px;
  padding: 1.5rem;
}

.filter-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.filter-modal-header h3 {
  font-size: 1.2rem;
  margin: 0;
}

.close-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
}

.filter-sections {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.filter-section h4 {
  font-size: 1rem;
  margin: 0 0 1rem;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  cursor: pointer;
}

.option-label {
  font-size: 0.9rem;
}

.filter-modal-actions {
  display: flex;
  justify-content: space-between;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.clear-btn, .show-btn {
  padding: 0.8rem 1.5rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.show-btn {
  background: var(--accent-color);
  color: var(--text-primary);
  border: none;
}

@media (max-width: 768px) {
  .jobs-container {
    flex-direction: column;
    padding: 1rem;
  }

  .jobs-sidebar {
    width: 100%;
    position: static;
  }
  
  .filter-chip {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
} 