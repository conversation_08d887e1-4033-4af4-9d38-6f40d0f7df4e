.messages-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  height: calc(100vh - 80px);
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.chat-list {
  background: var(--secondary-bg);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  box-shadow: 8px 8px 16px rgba(0, 0, 0, 0.2),
              -8px -8px 16px rgba(255, 255, 255, 0.05);
}

.chat-list-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-list-header h2 {
  margin: 0;
  color: var(--text-primary);
}

.new-message-btn {
  background: var(--accent-color);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.new-message-btn:hover {
  background: #2a9d7c;
}

.chat-list-content {
  flex: 1;
  overflow-y: auto;
}

.chat-item {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  cursor: pointer;
  transition: background 0.3s ease;
  border-bottom: 1px solid var(--border-color);
}

.chat-item:hover,
.chat-item.active {
  background: rgba(51, 189, 148, 0.1);
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 4px;
}

.chat-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
}

.chat-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.chat-preview {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-badge {
  background: var(--accent-color);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.chat-main {
  background: var(--secondary-bg);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  box-shadow: 8px 8px 16px rgba(0, 0, 0, 0.2),
              -8px -8px 16px rgba(255, 255, 255, 0.05);
}

.chat-main .chat-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 15px;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  margin-bottom: 10px;
}

.message.sent {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 12px;
  position: relative;
}

.message.received .message-content {
  background: rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 4px;
}

.message.sent .message-content {
  background: var(--accent-color);
  border-bottom-right-radius: 4px;
}

.message-content p {
  margin: 0;
  color: var(--text-primary);
  line-height: 1.4;
}

.message-time {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
  display: block;
}

.message.sent .message-time {
  text-align: right;
}

.message-input {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 10px;
}

.message-input input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  padding: 12px 15px;
  border-radius: 8px;
  color: var(--text-primary);
}

.message-input input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.15);
}

.message-input button {
  background: var(--accent-color);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.message-input button:hover {
  background: #2a9d7c;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 16px;
  text-align: center;
  padding: var(--spacing-xl);
}

@media (max-width: 768px) {
  .messages-container {
    grid-template-columns: 1fr;
  }

  .chat-list {
    display: none;
  }

  .chat-list.active {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
  }
} 