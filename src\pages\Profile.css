/* Profile Container */
.profile-container {
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  padding: 0;
}

/* LinkedIn-style Grid Layout */
.profile-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  align-items: start;
}

/* Main Content Column */
.profile-main-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-width: 0; /* Prevents overflow */
}

/* Sidebar Column */
.profile-sidebar-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: sticky;
  top: 100px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.profile-sidebar-column::-webkit-scrollbar {
  display: none;
}

/* Profile Header Card */
.profile-header-card {
  background: var(--bg-secondary);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.profile-header-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-elevated);
  border-color: rgba(51, 189, 148, 0.2);
}

.profile-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.profile-header:hover .cover-image {
  transform: scale(1.02);
}

.profile-info-section {
  padding: 20px;
  position: relative;
}

.profile-avatar-container {
  position: absolute;
  top: -60px;
  left: 20px;
}

.profile-avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid var(--bg-secondary);
  object-fit: cover;
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
}

.profile-avatar-large:hover {
  transform: scale(1.05);
  box-shadow: -8px -8px 16px rgba(255, 255, 255, 0.05),
              8px 8px 16px rgba(0, 0, 0, 0.3);
}

.profile-details-section {
  margin-left: 140px;
  margin-top: 20px;
  min-width: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 120px;
}

.profile-main-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 16px;
}

.profile-name-section {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.profile-username {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.profile-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--accent-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.profile-company {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.verified-badge {
  color: var(--accent-primary);
  font-size: 20px;
  filter: drop-shadow(0 0 4px rgba(51, 189, 148, 0.5));
}

.profile-name {
  font-size: 16px;
  font-weight: 400;
  margin: 0;
  color: var(--text-secondary);
}

.profile-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  background: var(--bg-primary);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  padding: 10px 16px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-outset);
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: -6px -6px 12px rgba(255, 255, 255, 0.05),
              6px 6px 12px rgba(0, 0, 0, 0.3);
}

.action-btn.follow {
  background: var(--accent-primary);
  color: var(--text-primary);
}

.action-btn.following {
  background: var(--bg-primary);
  color: var(--accent-primary);
}

.settings-btn {
  padding: 10px;
  min-width: auto;
}

.profile-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12px;
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
  min-width: 80px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: -4px -4px 8px rgba(255, 255, 255, 0.05),
              4px 4px 8px rgba(0, 0, 0, 0.3);
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.profile-bio {
  max-width: 500px;
  margin-top: 16px;
  clear: both;
}

.profile-bio p {
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 16px 0;
  color: var(--text-primary);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.profile-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.profile-link {
  color: var(--accent-primary);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.profile-link:hover {
  color: var(--accent-secondary);
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--bg-primary);
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-outset);
}

.social-link:hover {
  color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: -4px -4px 8px rgba(255, 255, 255, 0.05),
              4px 4px 8px rgba(0, 0, 0, 0.3);
}

/* Skills Section */
.skills-section {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-outset);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.skills-section:hover {
  transform: translateY(-2px);
  box-shadow: -8px -8px 16px rgba(255, 255, 255, 0.05),
              8px 8px 16px rgba(0, 0, 0, 0.3);
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 24px;
  background: var(--accent-primary);
  border-radius: var(--radius-full);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.skill-item {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: 16px;
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-2px);
  box-shadow: -4px -4px 8px rgba(255, 255, 255, 0.05),
              4px 4px 8px rgba(0, 0, 0, 0.3);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skill-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.skill-endorsements {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--accent-primary);
  font-weight: 500;
}

.skill-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  box-shadow: var(--shadow-inset);
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--radius-full);
  transition: width 0.8s ease;
  box-shadow: 0 0 10px rgba(51, 189, 148, 0.3);
}

/* Experience Section */
.experience-section {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-outset);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.experience-section:hover {
  transform: translateY(-2px);
  box-shadow: -8px -8px 16px rgba(255, 255, 255, 0.05),
              8px 8px 16px rgba(0, 0, 0, 0.3);
}

.experience-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.experience-item {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: 20px;
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
  border-left: 4px solid var(--accent-primary);
}

.experience-item:hover {
  transform: translateY(-2px);
  box-shadow: -4px -4px 8px rgba(255, 255, 255, 0.05),
              4px 4px 8px rgba(0, 0, 0, 0.3);
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.experience-header h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.experience-duration {
  font-size: 12px;
  color: var(--accent-primary);
  font-weight: 500;
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.experience-company {
  font-size: 14px;
  color: var(--accent-primary);
  font-weight: 500;
  margin: 0 0 8px 0;
}

.experience-description {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
  margin: 0;
}

/* Profile Tabs */
.profile-tabs {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-outset);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.tabs-container {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.tab-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  padding: 12px 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 2px solid transparent;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tab-btn:hover {
  color: var(--text-primary);
}

.tab-btn.active {
  color: var(--accent-primary);
  border-bottom-color: var(--accent-primary);
}

.tab-icon {
  font-size: 16px;
}

/* Posts Grid */
.posts-section {
  min-height: 400px;
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.grid-post {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.grid-post:hover {
  transform: translateY(-4px);
  box-shadow: -8px -8px 16px rgba(255, 255, 255, 0.05),
              8px 8px 16px rgba(0, 0, 0, 0.3);
  border-color: rgba(51, 189, 148, 0.2);
}

.post-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
}

.grid-post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.grid-post:hover .grid-post-image {
  transform: scale(1.05);
}

.video-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-size: 12px;
}

.post-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.grid-post:hover .post-overlay {
  opacity: 1;
}

.post-stats {
  display: flex;
  gap: 20px;
  color: var(--text-primary);
}

.post-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  background: none;
  box-shadow: none;
  padding: 0;
  min-width: auto;
}

.post-stats .stat-item svg {
  font-size: 16px;
}

/* Repositories Grid */
.repos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.repo-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 20px;
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
}

.repo-card:hover {
  transform: translateY(-4px);
  box-shadow: -8px -8px 16px rgba(255, 255, 255, 0.05),
              8px 8px 16px rgba(0, 0, 0, 0.3);
  border-color: rgba(51, 189, 148, 0.2);
}

.repo-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.repo-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.repo-language {
  font-size: 12px;
  color: var(--accent-primary);
  background: var(--bg-primary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.repo-description {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.repo-stats {
  display: flex;
  gap: 16px;
}

.repo-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-secondary);
}

/* Resume Section */
.resume-section {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 24px;
  box-shadow: var(--shadow-outset);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.resume-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.resume-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.resume-content {
  color: var(--text-primary);
  line-height: 1.6;
}

.resume-content p {
  margin: 0 0 16px 0;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: var(--text-secondary);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--bg-secondary);
  box-shadow: var(--shadow-outset);
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .posts-grid,
  .repos-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .profile-stats {
    gap: 24px;
  }
  
  .tabs-container {
    gap: 30px;
  }
  
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .profile-cover {
    height: 150px;
  }
  
  .profile-avatar-large {
    width: 80px;
    height: 80px;
  }
  
  .profile-details-section {
    margin-left: 100px;
    margin-top: 10px;
  }
  
  .profile-username {
    font-size: 20px;
  }
  
  .profile-title {
    font-size: 16px;
  }
  
  .profile-main-info {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .profile-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .action-btn {
    font-size: 13px;
    padding: 8px 12px;
  }
  
  .profile-stats {
    gap: 16px;
    margin-bottom: 16px;
  }
  
  .stat-number {
    font-size: 16px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .tabs-container {
    gap: 20px;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  
  .tab-btn {
    font-size: 13px;
  }
  
  .posts-grid,
  .repos-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }
  
  .skills-grid {
    grid-template-columns: 1fr;
  }
  
  .experience-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .social-links {
    gap: 8px;
  }
  
  .social-link {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .profile-header {
    margin-bottom: 16px;
  }
  
  .profile-cover {
    height: 120px;
  }
  
  .profile-info-section {
    padding: 16px;
  }
  
  .profile-avatar-container {
    top: -40px;
    left: 16px;
  }
  
  .profile-avatar-large {
    width: 60px;
    height: 60px;
  }
  
  .profile-details-section {
    margin-left: 80px;
    margin-top: 5px;
  }
  
  .profile-username {
    font-size: 18px;
  }
  
  .profile-title {
    font-size: 14px;
  }
  
  .action-btn {
    font-size: 12px;
    padding: 6px 10px;
  }
  
  .profile-stats {
    gap: 12px;
  }
  
  .stat-item {
    padding: 8px;
    min-width: 60px;
  }
  
  .stat-number {
    font-size: 14px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .profile-bio p {
    font-size: 13px;
  }
  
  .tabs-container {
    gap: 16px;
  }
  
  .tab-btn {
    font-size: 12px;
    padding: 10px 0;
  }
  
  .posts-grid,
  .repos-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .skills-section,
  .experience-section {
    padding: 16px;
  }
  
  .section-title {
    font-size: 18px;
  }
  
  .skill-item,
  .experience-item {
    padding: 12px;
  }
  
  .empty-state {
    padding: 40px 16px;
  }
  
  .empty-icon {
    font-size: 36px;
    width: 60px;
    height: 60px;
  }
  
  .empty-state h3 {
    font-size: 16px;
  }
  
  .empty-state p {
    font-size: 13px;
  }
}

/* Sidebar Cards */
.profile-stats-card,
.quick-actions-card,
.activity-summary-card {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 20px;
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.profile-stats-card:hover,
.quick-actions-card:hover,
.activity-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-elevated);
  border-color: rgba(255, 255, 255, 0.08);
}

.profile-stats-card .section-title,
.quick-actions-card .section-title,
.activity-summary-card .section-title {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Profile Stats in Sidebar */
.profile-stats-card .profile-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.profile-stats-card .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-inset);
  transition: all 0.3s ease;
  min-height: 50px;
}

.profile-stats-card .stat-item:hover {
  background: rgba(255, 255, 255, 0.02);
  transform: translateX(4px);
}

.profile-stats-card .stat-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--accent-color);
  white-space: nowrap;
}

.profile-stats-card .stat-label {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--bg-primary);
  border: none;
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-inset);
  text-align: left;
  width: 100%;
  min-height: 44px;
  white-space: nowrap;
  overflow: hidden;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.02);
  transform: translateX(4px);
  color: var(--accent-color);
}

.quick-action-btn svg {
  font-size: 14px;
  color: var(--accent-color);
  flex-shrink: 0;
}

.quick-action-btn span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Activity Summary */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-inset);
  transition: all 0.3s ease;
  min-height: 60px;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.02);
  transform: translateX(2px);
}

.activity-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bg-primary);
  font-size: 12px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(51, 189, 148, 0.3);
  margin-top: 2px;
}

.activity-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.activity-content p {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-time {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 400;
  white-space: nowrap;
}

/* Trending Topics */
.trending-topics-card {
  padding: 20px;
}

.trending-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trending-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-inset);
  transition: all 0.3s ease;
  min-height: 44px;
}

.trending-item:hover {
  background: rgba(255, 255, 255, 0.02);
  transform: translateX(4px);
  cursor: pointer;
}

.trending-tag {
  font-size: 13px;
  font-weight: 600;
  color: var(--accent-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.trending-posts {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

/* Responsive Design for LinkedIn-style Layout */

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .profile-layout {
    grid-template-columns: 1fr 340px;
    gap: 32px;
    max-width: 1400px;
    padding: 32px;
  }

  .profile-main-column {
    gap: 28px;
  }

  .profile-sidebar-column {
    gap: 24px;
  }
}

/* Medium Desktop (1024px - 1439px) */
@media (max-width: 1439px) and (min-width: 1024px) {
  .profile-layout {
    grid-template-columns: 1fr 300px;
    gap: 20px;
    max-width: 1200px;
    padding: 20px;
  }

  .profile-main-column {
    gap: 20px;
  }

  .profile-sidebar-column {
    gap: 16px;
  }

  .profile-stats-card,
  .quick-actions-card,
  .activity-summary-card {
    padding: 16px;
  }
}

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .profile-layout {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 100%;
    padding: 16px;
  }

  .profile-sidebar-column {
    position: static;
    max-height: none;
    overflow-y: visible;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }

  .profile-main-column {
    gap: 20px;
  }

  .profile-cover {
    height: 180px;
  }

  .profile-avatar-large {
    width: 100px;
    height: 100px;
  }

  .profile-details-section {
    margin-left: 120px;
    margin-top: 15px;
  }

  .profile-username {
    font-size: 22px;
  }

  .profile-title {
    font-size: 16px;
  }
}

/* Mobile (max-width: 767px) */
@media (max-width: 767px) {
  .profile-layout {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 12px;
  }

  .profile-sidebar-column {
    position: static;
    max-height: none;
    overflow-y: visible;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .profile-main-column {
    gap: 16px;
  }

  .profile-cover {
    height: 150px;
  }

  .profile-avatar-large {
    width: 80px;
    height: 80px;
  }

  .profile-details-section {
    margin-left: 100px;
    margin-top: 10px;
  }

  .profile-username {
    font-size: 20px;
  }

  .profile-title {
    font-size: 14px;
  }

  .profile-main-info {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .profile-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .action-btn {
    font-size: 13px;
    padding: 8px 12px;
  }

  .profile-stats-card,
  .quick-actions-card,
  .activity-summary-card {
    padding: 16px;
  }

  .posts-grid,
  .repos-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .tabs-container {
    gap: 16px;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .tab-btn {
    font-size: 13px;
    padding: 10px 16px;
  }
}

/* Neomorphic Cards */
.neomorphic-card {
  background: var(--bg-secondary);
  border-radius: 16px;
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.neomorphic-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-elevated);
  border-color: rgba(51, 189, 148, 0.2);
}

.neomorphic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.neomorphic-card:hover::before {
  opacity: 0.6;
}

/* Section Cards */
.about-section,
.skills-section,
.experience-section,
.profile-tabs {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.about-section:hover,
.skills-section:hover,
.experience-section:hover,
.profile-tabs:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neumorphic-elevated);
  border-color: rgba(255, 255, 255, 0.08);
}