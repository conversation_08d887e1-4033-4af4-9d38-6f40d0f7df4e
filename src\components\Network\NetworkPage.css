.network-container {
  display: flex;
  gap: 1.5rem;
  max-width: 1140px;
  margin: 0 auto;
  padding: 1.5rem;
}

.network-sidebar {
  width: 300px;
  border-radius: 8px;
  padding: 1rem;
  flex-shrink: 0;
  align-self: flex-start;
  position: sticky;
  top: 80px;
}

.sidebar-header {
  margin-bottom: 1rem;
}

.sidebar-header h2 {
  font-size: 1.2rem;
  margin: 0;
  padding: 0.5rem 0;
}

.network-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.network-links li {
  margin-bottom: 0.5rem;
}

.network-link {
  display: flex;
  align-items: center;
  padding: 0.8rem;
  text-decoration: none;
  color: var(--text-primary);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.network-link:hover {
  background: var(--input-bg);
}

.network-icon {
  margin-right: 1rem;
  font-size: 1.2rem;
  color: var(--accent-color);
}

.count {
  margin-left: auto;
  color: var(--text-secondary);
}

.network-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.invitations-section, .people-section {
  border-radius: 8px;
  padding: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  font-size: 1.2rem;
  margin: 0;
}

.see-all-link {
  color: var(--accent-color);
  text-decoration: none;
  font-size: 0.9rem;
}

.invitations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.invitation-card {
  display: flex;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
}

.invitation-card:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1rem;
  flex-shrink: 0;
}

.user-avatar.large {
  width: 72px;
  height: 72px;
  margin: 0 auto 1rem;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.invitation-details {
  flex: 1;
}

.invitation-details h3 {
  font-size: 1rem;
  margin: 0 0 0.3rem;
}

.user-title {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem;
}

.mutual-connections {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0 0 0.3rem;
  display: flex;
  align-items: center;
}

.mutual-icon {
  margin-right: 0.5rem;
  font-size: 0.8rem;
}

.invitation-time {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0;
}

.invitation-actions {
  display: flex;
  gap: 0.8rem;
  margin-left: 1rem;
  align-self: center;
}

.ignore-btn, .accept-btn {
  padding: 0.6rem 1.2rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ignore-btn {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.ignore-btn:hover {
  background: var(--input-bg);
}

.accept-btn {
  background: var(--accent-color);
  color: var(--text-primary);
  border: none;
}

.accept-btn:hover {
  background: var(--accent-color);
  opacity: 0.9;
}

.people-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.connection-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  border-radius: 8px;
  background: var(--secondary-bg);
  transition: all 0.3s ease;
  text-align: center;
}

.connection-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.connection-card h3 {
  font-size: 1rem;
  margin: 0 0 0.3rem;
}

.connect-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.6rem 1.2rem;
  border-radius: 15px;
  background: transparent;
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.connect-btn:hover {
  background: var(--accent-color);
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .network-container {
    flex-direction: column;
    padding: 1rem;
  }

  .network-sidebar {
    width: 100%;
    position: static;
    margin-bottom: 1rem;
  }

  .people-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .invitation-card {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.5rem 0;
  }

  .user-avatar {
    margin: 0 0 1rem;
  }

  .invitation-actions {
    margin: 1rem 0 0;
  }
} 