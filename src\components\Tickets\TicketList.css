.tickets-container {
  max-width: 1200px;
  margin: 80px auto 0;
  padding: 2rem;
}

.tickets-header {
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-radius: 15px;
}

.tickets-header h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.tickets-filters {
  display: flex;
  gap: 1rem;
  padding: 0.5rem;
  background: var(--input-bg);
  border-radius: 12px;
}

.filter-btn {
  padding: 0.8rem 1.2rem;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: var(--accent-color);
  color: var(--text-primary);
}

.tickets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.ticket-card {
  padding: 1.5rem;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.ticket-card:hover {
  transform: translateY(-5px);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.ticket-header h3 {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin: 0;
}

.ticket-type {
  background: var(--input-bg);
  color: var(--accent-color);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

.ticket-company {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.ticket-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.ticket-icon {
  color: var(--accent-color);
}

.ticket-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.skill-tag {
  background: var(--input-bg);
  color: var(--text-primary);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

.apply-btn {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  background: var(--accent-color);
  color: var(--text-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  opacity: 0.9;
}

@media (max-width: 768px) {
  .tickets-container {
    padding: 1rem;
  }

  .tickets-filters {
    overflow-x: auto;
    padding: 0.5rem;
  }

  .filter-btn {
    white-space: nowrap;
  }
} 