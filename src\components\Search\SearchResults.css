.search-results-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 1rem;
}

.search-bar-container {
  margin-bottom: 1.5rem;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 0.8rem 1.2rem;
  background: var(--input-bg);
  border-radius: 12px;
}

.search-icon {
  color: var(--text-secondary);
  margin-right: 0.8rem;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1rem;
}

.search-input:focus {
  outline: none;
}

.search-filters {
  margin-bottom: 2rem;
}

.search-filters h3 {
  font-size: 1.2rem;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.filter-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.2rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.filter-buttons::-webkit-scrollbar {
  display: none;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.7rem 1.2rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: var(--accent-color);
  color: var(--text-primary);
  border-color: var(--accent-color);
}

.filter-btn:hover:not(.active) {
  background: var(--input-bg);
}

.filter-dropdowns {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-dropdown {
  position: relative;
  flex: 1;
  min-width: 120px;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.8rem 1.2rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropdown-toggle:hover {
  background: var(--input-bg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 250px;
  overflow-y: auto;
}

.dropdown-item {
  width: 100%;
  padding: 0.8rem 1.2rem;
  background: transparent;
  border: none;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 0.9rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: var(--input-bg);
}

.search-results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.result-item {
  background: var(--secondary-bg);
  border-radius: 12px;
  padding: 1.5rem;
  aspect-ratio: 1;
}

.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .filter-dropdowns {
    flex-direction: column;
  }

  .filter-dropdown {
    width: 100%;
  }

  .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
} 