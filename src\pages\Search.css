/* Reset container styles */
.search-container,
.search-page {
  all: unset;
}

/* Search Container */
.search-container {
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Search Header */
.search-header {
  margin-bottom: 24px;
}

.search-input-container {
  position: relative;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,.25);
}

.search-icon {
  position: absolute;
  left: 28px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 16px;
  pointer-events: none;
}

.search-input {
  width: 100%;
  background: var(--bg-primary);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: 16px;
  padding: 12px 48px 12px 48px;
  box-shadow: 0 2px 4px rgba(0,0,0,.25);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-primary);
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.clear-search {
  position: absolute;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.clear-search:hover {
  color: var(--accent-primary);
  transform: translateY(-50%) scale(1.1);
}

/* Search Tabs */
.search-tabs {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,.25);
}

.tabs-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  scrollbar-width: none;
  padding: 4px 0;
}

.tabs-scroll::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  background: var(--bg-primary);
  border: none;
  border-radius: var(--radius-full);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0,0,0,.25);
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-btn:hover {
  color: var(--accent-primary);
  transform: translateY(-1px);
}

.tab-btn.active {
  background: var(--accent-primary);
  color: var(--text-primary);
  box-shadow: 0 2px 4px rgba(0,0,0,.25);
}

.tab-icon {
  font-size: 12px;
}

/* Recent Searches */
.recent-searches {
  width: 100%;
  max-width: 860px;
  margin: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.section-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.clear-all {
  background: none;
  border: none;
  color: var(--accent-color);
  font-size: 0.9rem;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.clear-all:hover {
  background: rgba(51, 189, 148, 0.1);
}

/* Recent List */
.recent-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-lg);
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background: var(--background);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 4px rgba(0,0,0,.25);
}

.recent-item:hover {
  background: rgba(255, 255, 255, 0.02);
  transform: translateY(-1px);
}

.recent-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.recent-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
}

.recent-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: var(--accent-darker);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.recent-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.recent-text {
  color: var(--text-primary);
  font-weight: 500;
}

.recent-type {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.remove-recent {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: var(--spacing-xs);
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  transition: all var(--transition-fast);
}

.recent-item:hover .remove-recent {
  opacity: 1;
}

.remove-recent:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

/* Search Results */
.search-results {
  width: 100%;
}

.search-tabs {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: var(--background);
  position: sticky;
  top: 0;
  z-index: 10;
}

.tabs-scroll {
  display: flex;
  gap: var(--spacing-sm);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: var(--spacing-xs);
}

.tabs-scroll::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  white-space: nowrap;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.tab-btn.active {
  background: var(--accent-color);
  color: var(--background);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}

.result-item {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background: var(--background);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 4px rgba(0,0,0,.25);
}

.result-item:hover {
  background: rgba(255, 255, 255, 0.02);
  transform: translateY(-1px);
}

/* User Result */
.user-result {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
}

.username {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 2px;
}

.followers {
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-top: 2px;
}

.follow-btn {
  background: var(--accent-color);
  color: var(--background);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.follow-btn:hover {
  background: var(--accent-secondary);
}

/* Hashtag Result */
.hashtag-result {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.hashtag-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: var(--accent-darker);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.hashtag-info {
  flex: 1;
}

.hashtag-name {
  font-weight: 600;
  color: var(--text-primary);
}

.hashtag-posts {
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-top: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-header,
  .results-list,
  .recent-list {
    padding: var(--spacing-md);
  }

  .recent-avatar,
  .recent-icon,
  .user-avatar,
  .hashtag-icon {
    width: 36px;
    height: 36px;
  }

  .follow-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.8rem;
  }
}

/* Search Page Layout */
.search-page {
  width: 100%;
  min-height: calc(100vh - 64px);
  padding: 0;
  display: flex;
  justify-content: flex-start;
  background: var(--background);
}

.search-container {
  width: 100%;
  max-width: 680px;
  margin: 0;
  background: var(--secondary-bg);
  border-radius: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  min-height: calc(100vh - 64px);
}

/* Search Content */
.search-content {
  padding: var(--spacing-lg);
  max-width: 100%;
}

/* Search Results List */
.results-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-md);
}

/* Recent Items */
.recent-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-md);
}

/* Search Tabs */
.search-tabs {
  padding: var(--spacing-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: var(--background);
  position: sticky;
  top: 0;
  z-index: 10;
}

.tabs-scroll {
  display: flex;
  gap: var(--spacing-sm);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: var(--spacing-xs);
}

.tabs-scroll::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  white-space: nowrap;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.tab-btn.active {
  background: var(--accent-color);
  color: var(--background);
}

/* Responsive Design */
@media (min-width: 1280px) {
  .search-container {
    margin-left: var(--sidebar-width);
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .search-container {
    margin-left: var(--sidebar-collapsed-width);
  }
}

@media (max-width: 1023px) {
  .search-container {
    margin-left: 0;
    border-radius: 0;
  }
}

@media (max-width: 768px) {
  .search-content {
    padding: var(--spacing-md);
  }

  .section-header,
  .results-list,
  .recent-list {
    padding: 0;
  }

  .recent-avatar,
  .recent-icon,
  .user-avatar,
  .hashtag-icon {
    width: 36px;
    height: 36px;
  }
} 