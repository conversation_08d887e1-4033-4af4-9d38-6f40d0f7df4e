.comments-section {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  max-width: 500px;
  background: var(--primary-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.comments-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.comments-header h2 {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin: 0;
}

.close-comment-btn {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1.1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.header-space {
  width: 24px;
}

.comments-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.comment-item {
  display: flex;
  margin-bottom: 1.5rem;
  gap: 0.8rem;
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img,
.current-user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-author {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.comment-text {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.comment-meta {
  display: flex;
  gap: 1rem;
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.like-comment-btn {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.3rem;
  margin-top: 0.2rem;
}

.like-comment-btn:hover {
  color: #ff4d4d;
}

.like-comment-btn.active {
  color: #ff4d4d;
}

.comment-input-area {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.current-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: var(--secondary-bg);
  border-radius: 20px;
  padding: 0.3rem 0.8rem;
}

.comment-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 0.8rem 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.comment-input:focus {
  outline: none;
}

.post-comment-btn {
  background: transparent;
  border: none;
  color: var(--accent-color);
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem;
  font-size: 0.9rem;
}

.post-comment-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 600px) {
  .comments-section {
    left: 0;
    max-width: none;
  }
}

@media (min-width: 601px) {
  .comments-section {
    border-left: 1px solid var(--border-color);
  }
} 