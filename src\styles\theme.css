:root {
  /* Color Palette */
  --bg-primary: #002626;
  --bg-secondary: #001a1a;
  --accent-primary: #33BD94;
  --accent-secondary: #2a9d7c;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --error: #ff4d4d;
  --success: #33BD94;

  /* Aliases for compatibility */
  --background: var(--bg-primary);
  --secondary-bg: var(--bg-secondary);
  --accent-color: var(--accent-primary);

  /* Layout Variables */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;

  /* Neomorphic Shadows */
  --shadow-outset: -8px -8px 12px rgba(255, 255, 255, 0.05),
                   8px 8px 12px rgba(0, 0, 0, 0.2);
  --shadow-inset: inset -8px -8px 12px rgba(255, 255, 255, 0.05),
                  inset 8px 8px 12px rgba(0, 0, 0, 0.2);

  /* Enhanced <PERSON>eumorphic Shadows for Components - Smoother */
  --shadow-neumorphic: 
    4px 4px 8px rgba(0, 0, 0, 0.25),
    -2px -2px 6px rgba(255, 255, 255, 0.02),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.03);
  
  --shadow-neumorphic-hover: 
    6px 6px 12px rgba(0, 0, 0, 0.3),
    -3px -3px 8px rgba(255, 255, 255, 0.025),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.05);
  
  --shadow-neumorphic-inset: 
    inset 2px 2px 4px rgba(0, 0, 0, 0.2),
    inset -1px -1px 3px rgba(255, 255, 255, 0.015);

  --shadow-neumorphic-soft: 
    2px 2px 6px rgba(0, 0, 0, 0.15),
    -1px -1px 4px rgba(255, 255, 255, 0.015),
    inset 0 0 0 0.5px rgba(255, 255, 255, 0.025);

  --shadow-neumorphic-elevated: 
    8px 8px 16px rgba(0, 0, 0, 0.35),
    -4px -4px 10px rgba(255, 255, 255, 0.03),
    inset 0 0 0 1px rgba(255, 255, 255, 0.06);

  /* Transitions - Smoother */
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 20px;
  --radius-full: 9999px;

  /* Typography */
  --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  /* Tab & Button sizing */
  --btn-padding: 1.05rem 1.25rem;
  --btn-font-size: 1rem;
}

/* Base Styles */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-primary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Tab & Button Sizing */
.tab-btn, .primary-btn {
  padding: var(--btn-padding);
  font-size: var(--btn-font-size);
}

/* Common Components */
.neomorphic-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-outset);
  padding: 0;
  margin: 0;
  overflow: hidden;
}

/* Consistent post card dimensions */
.post-card,
.grid-post,
.post-gallery-card {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* Media containers should fill completely */
.post-image,
.post-image-container,
.grid-post-image {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
}

.post-content {
  padding: var(--spacing-md);
  transition: all 0.3s ease;
}

.neomorphic-button {
  background-color: var(--bg-secondary);
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 1rem;
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-outset);
  transition: all 0.3s ease;
}

.neomorphic-button:hover {
  background-color: var(--accent-primary);
  transform: translateY(-2px);
}

.neomorphic-button:active {
  box-shadow: var(--shadow-inset);
  transform: translateY(0);
}

.neomorphic-input {
  background-color: var(--bg-secondary);
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  padding: var(--spacing-md);
  width: 100%;
  box-shadow: var(--shadow-inset);
  transition: all 0.3s ease;
}

.neomorphic-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-primary);
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

/* Responsive Breakpoints */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
} 